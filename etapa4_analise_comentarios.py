import json
import os
import asyncio
from openai import AsyncOpenAI
from dotenv import load_dotenv
import time
from typing import List, Dict

# Carrega variáveis de ambiente
load_dotenv()

class AnalisadorComentariosRapido:
    """Analisador de comentários com processamento concorrente para máxima velocidade"""
    
    def __init__(self, max_concurrent_requests=3, requests_per_minute=150):
        self.client = AsyncOpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        self.max_concurrent = max_concurrent_requests
        self.requests_per_minute = requests_per_minute
        self.semaphore = asyncio.Semaphore(max_concurrent_requests)
        self.request_times = []
        
    def load_prompt_template(self):
        """Carrega template do prompt"""
        try:
            with open('prompt_analise_comentario.md', 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"⚠️ Erro ao carregar prompt: {e}")
            return None

    def reorder_fields(self, analysis_result: Dict) -> Dict:
        """Reorganiza campos em ordem consistente"""

        # Ordem desejada dos campos
        field_order = [
            'id_video',
            'id_comentario',
            'comentario_util',
            'titulo_video',
            'comentario',
            'categoria',
            'motivacao',
            'medos',
            'dificuldades',
            'valores',
            'sonhos',
            'emocao_principal',
            'linguagem',
            'resumo',
            'oportunidades_negocio',
            'ideias_conteudo',
            'motivo'  # Para comentários não úteis
        ]

        # Cria novo dicionário ordenado
        ordered_result = {}

        # Adiciona campos na ordem especificada (se existirem)
        for field in field_order:
            if field in analysis_result:
                ordered_result[field] = analysis_result[field]

        # Adiciona campos extras que não estão na ordem (se houver)
        for field, value in analysis_result.items():
            if field not in ordered_result:
                ordered_result[field] = value

        return ordered_result
    
    async def rate_limit_wait(self):
        """Controla rate limiting de forma inteligente"""
        now = time.time()
        
        # Remove requisições antigas (mais de 1 minuto)
        self.request_times = [t for t in self.request_times if now - t < 60]
        
        # Se estamos próximos do limite, aguarda (mais conservador)
        if len(self.request_times) >= self.requests_per_minute * 0.6:  # 60% do limite
            wait_time = 60 - (now - self.request_times[0]) + 2  # +2s margem
            if wait_time > 0:
                print(f"   ⏳ Rate limiting preventivo: aguardando {wait_time:.1f}s...")
                await asyncio.sleep(wait_time)
                self.request_times = []

        self.request_times.append(now)
    
    async def analyze_single_comment(self, comment_data: Dict, video_title: str, 
                                   video_id: str, prompt_template: str) -> Dict:
        """Analisa um único comentário de forma assíncrona"""
        
        async with self.semaphore:  # Limita concorrência
            await self.rate_limit_wait()  # Controla rate limiting
            
            try:
                comment_text = comment_data.get('texto_comentario', '')
                comment_id = comment_data.get('id_comentario', '')
                
                # Monta prompt específico
                analysis_prompt = prompt_template.replace('[AQUI]', f'"{video_title}"', 1)
                analysis_prompt = analysis_prompt.replace('[AQUI]', f'"{comment_text}"', 1)
                
                # Faz requisição assíncrona
                response = await self.client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[
                        {
                            "role": "system", 
                            "content": "Você é uma especialista em análise de audiência. Responda APENAS com JSON válido."
                        },
                        {
                            "role": "user", 
                            "content": analysis_prompt
                        }
                    ],
                    temperature=0.3,
                    max_tokens=800
                )
                
                # Processa resposta
                ai_response = response.choices[0].message.content.strip()
                analysis_result = json.loads(ai_response)

                # Adiciona metadados apenas se não estiverem presentes
                if 'id_video' not in analysis_result:
                    analysis_result['id_video'] = video_id
                if 'titulo_video' not in analysis_result:
                    analysis_result['titulo_video'] = video_title
                if 'id_comentario' not in analysis_result:
                    analysis_result['id_comentario'] = comment_id
                if 'comentario' not in analysis_result:
                    analysis_result['comentario'] = comment_text

                # Padroniza campo (compatibilidade com versão anterior)
                if 'util' in analysis_result:
                    analysis_result['comentario_util'] = analysis_result.pop('util')

                # Reorganiza campos em ordem consistente
                ordered_result = self.reorder_fields(analysis_result)

                return ordered_result
                
            except json.JSONDecodeError as e:
                print(f"⚠️ Erro JSON para comentário {comment_id}: {e}")
                return None
            except Exception as e:
                error_str = str(e)

                # Tratamento específico para rate limit (429)
                if "429" in error_str or "rate limit" in error_str.lower():
                    print(f"⏰ Rate limit atingido para comentário {comment_id}")
                    # Extrai tempo de espera da mensagem de erro
                    if "try again in" in error_str:
                        import re
                        match = re.search(r'try again in (\d+)ms', error_str)
                        if match:
                            wait_ms = int(match.group(1))
                            wait_seconds = (wait_ms / 1000) + 1  # +1s de margem
                            print(f"   ⏳ Aguardando {wait_seconds:.1f}s...")
                            await asyncio.sleep(wait_seconds)

                            # Tenta novamente UMA vez
                            try:
                                print(f"   🔄 Tentativa 2/2 para comentário {comment_id}")
                                response = await self.client.chat.completions.create(
                                    model="gpt-4o-mini",
                                    messages=[
                                        {
                                            "role": "user",
                                            "content": analysis_prompt
                                        }
                                    ],
                                    temperature=0.3,
                                    max_tokens=800
                                )

                                ai_response = response.choices[0].message.content.strip()
                                analysis_result = json.loads(ai_response)

                                # Adiciona metadados
                                if 'id_video' not in analysis_result:
                                    analysis_result['id_video'] = video_id
                                if 'titulo_video' not in analysis_result:
                                    analysis_result['titulo_video'] = video_title
                                if 'id_comentario' not in analysis_result:
                                    analysis_result['id_comentario'] = comment_id
                                if 'comentario' not in analysis_result:
                                    analysis_result['comentario'] = comment_text

                                return self.reorder_fields(analysis_result)

                            except Exception as retry_error:
                                print(f"❌ Falha na segunda tentativa para {comment_id}: {retry_error}")
                                return None

                print(f"❌ Erro na análise do comentário {comment_id}: {e}")
                return None
    
    async def analyze_video_comments(self, video_data: Dict, comments: List[Dict], 
                                   prompt_template: str) -> List[Dict]:
        """Analisa todos os comentários de um vídeo de forma concorrente"""
        
        video_id = video_data.get('id_video')
        video_title = video_data.get('titulo_video', '')
        
        print(f"   🚀 Analisando {len(comments)} comentários concorrentemente...")
        start_time = time.time()
        
        # Cria tasks para todos os comentários
        tasks = []
        for comment in comments:
            task = self.analyze_single_comment(
                comment, video_title, video_id, prompt_template
            )
            tasks.append(task)
        
        # Executa todas as análises concorrentemente
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filtra resultados válidos
        valid_results = []
        for result in results:
            if isinstance(result, dict):
                valid_results.append(result)
            elif isinstance(result, Exception):
                print(f"⚠️ Erro em análise: {result}")
        
        elapsed = time.time() - start_time
        print(f"   ✅ {len(valid_results)}/{len(comments)} comentários analisados em {elapsed:.1f}s")
        
        return valid_results
    
    async def process_all_videos(self, relevant_videos: List[Dict], channel_folder: str):
        """Processa todos os vídeos relevantes"""
        
        prompt_template = self.load_prompt_template()
        if not prompt_template:
            return [], 0
        
        # Carrega análises existentes
        analyses_folder = os.path.join(channel_folder, 'analises')
        existing_analyses = self.load_existing_analyses(analyses_folder)
        analyzed_comment_ids = {a.get('id_comentario') for a in existing_analyses}
        
        all_analyses = existing_analyses.copy()
        total_new_analyses = 0
        
        for i, video in enumerate(relevant_videos):
            video_id = video.get('id_video')
            video_title = video.get('titulo_video', '')
            
            print(f"\n🎬 Vídeo {i + 1}/{len(relevant_videos)}: {video_title[:50]}...")
            
            # Carrega comentários
            print(f"   📂 Carregando comentários do vídeo {video_id}...")
            comments = self.load_video_comments(video_id, channel_folder)
            if not comments:
                print(f"   ⚠️ Nenhum comentário encontrado para o vídeo {video_id}")
                continue
            
            # Filtra comentários não analisados
            new_comments = [
                c for c in comments 
                if c.get('id_comentario') not in analyzed_comment_ids
            ]
            
            if not new_comments:
                print(f"   ✅ Todos os comentários já analisados")
                continue
            
            print(f"   📝 {len(new_comments)} comentários novos para analisar")
            
            # Analisa comentários concorrentemente
            video_analyses = await self.analyze_video_comments(
                video, new_comments, prompt_template
            )
            
            # Adiciona às análises totais
            all_analyses.extend(video_analyses)
            total_new_analyses += len(video_analyses)
            
            # Atualiza IDs analisados
            for analysis in video_analyses:
                analyzed_comment_ids.add(analysis.get('id_comentario'))
            
            # Salva progresso
            self.save_analyses(all_analyses, analyses_folder)
        
        return all_analyses, total_new_analyses
    
    def load_video_comments(self, video_id: str, channel_folder: str) -> List[Dict]:
        """Carrega comentários de um vídeo"""
        comments_file = os.path.join(channel_folder, 'comentarios', f"{video_id}_comentarios.json")
        
        if not os.path.exists(comments_file):
            return []
        
        try:
            with open(comments_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data.get('comentarios', [])
        except Exception as e:
            print(f"⚠️ Erro ao carregar comentários {video_id}: {e}")
            return []
    
    def load_existing_analyses(self, analyses_folder: str) -> List[Dict]:
        """Carrega análises existentes"""
        file_path = os.path.join(analyses_folder, 'comentarios.json')
        
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠️ Erro ao carregar análises existentes: {e}")
        
        return []
    
    def save_analyses(self, analyses: List[Dict], analyses_folder: str):
        """Salva análises"""
        try:
            os.makedirs(analyses_folder, exist_ok=True)
            file_path = os.path.join(analyses_folder, 'comentarios.json')
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(analyses, f, ensure_ascii=False, indent=2)
            
            print(f"   💾 {len(analyses)} análises salvas")
        except Exception as e:
            print(f"⚠️ Erro ao salvar: {e}")

# Função principal para usar a versão rápida
async def analisar_comentarios_rapido(relevant_videos: List[Dict], channel_folder: str):
    """Função principal para análise rápida de comentários"""
    
    print("🚀 MODO RÁPIDO: Análise Concorrente de Comentários")
    print("=" * 60)
    
    analisador = AnalisadorComentariosRapido(
        max_concurrent_requests=3,  # REDUZIDO: 3 requisições simultâneas
        requests_per_minute=150     # REDUZIDO: Limite mais conservador
    )
    
    start_time = time.time()
    
    all_analyses, new_analyses = await analisador.process_all_videos(
        relevant_videos, channel_folder
    )
    
    elapsed = time.time() - start_time
    
    print(f"\n🎉 ANÁLISE CONCLUÍDA!")
    print(f"   ⏱️ Tempo total: {elapsed:.1f}s")
    print(f"   📊 Novos comentários analisados: {new_analyses}")
    print(f"   📁 Total de análises: {len(all_analyses)}")
    
    if new_analyses > 0:
        print(f"   🚀 Velocidade: {new_analyses/elapsed:.1f} comentários/segundo")
    
    return all_analyses, new_analyses

class Etapa4AnaliseComentarios:
    """Classe para análise de comentários com IA (versão rápida/concorrente)"""
    
    def __init__(self):
        pass
    
    def verificar_videos_relevantes_disponiveis(self, channel_folder):
        """Verifica se há vídeos relevantes disponíveis para análise de comentários"""
        analyses_file = os.path.join(channel_folder, 'analises', 'videos.json')
        
        if not os.path.exists(analyses_file):
            return False, []
        
        try:
            with open(analyses_file, 'r', encoding='utf-8') as f:
                all_videos = json.load(f)
            
            # Filtra apenas vídeos relevantes
            relevant_videos = [video for video in all_videos if video.get('relevante', False)]
            print(f"📹 Encontrados {len(relevant_videos)} vídeos relevantes para análise de comentários")
            return True, relevant_videos
            
        except Exception as e:
            print(f"⚠️ Erro ao carregar análises de vídeos: {e}")
            return False, []
    
    def executar(self, channel_folder):
        """Executa a análise de comentários com IA (versão rápida)"""
        print("🎯 ETAPA 4: Análise de Comentários com IA (Modo Rápido)")
        print("=" * 60)
        
        # Verifica se há vídeos relevantes disponíveis
        tem_videos, relevant_videos = self.verificar_videos_relevantes_disponiveis(channel_folder)
        
        if not tem_videos:
            return "❌ Nenhum vídeo relevante encontrado. Execute a Etapa 2 primeiro."
        
        if not relevant_videos:
            return "⚠️ Lista de vídeos relevantes está vazia."
        
        # Executa análise concorrente
        try:
            all_analyses, new_analyses = asyncio.run(
                analisar_comentarios_rapido(relevant_videos, channel_folder)
            )
            
            if len(all_analyses) > 0:
                return f"✅ Análise de comentários concluída! {new_analyses} novos comentários analisados. Total: {len(all_analyses)} análises."
            else:
                return "❌ Nenhuma análise de comentário foi realizada. Verifique a configuração da API OpenAI."
                
        except Exception as e:
            return f"❌ Erro durante a análise: {e}"

if __name__ == "__main__":
    # Teste da etapa 4
    etapa4 = Etapa4AnaliseComentarios()
    
    # Exemplo de uso
    channel_folder = "Mayra Gaiato _ Desenvolvimento Infantil e Autismo"
    if os.path.exists(channel_folder):
        result = etapa4.executar(channel_folder)
        print(f"\n🎉 Resultado: {result}")
    else:
        print("❌ Pasta do canal não encontrada. Execute as etapas anteriores primeiro.")
