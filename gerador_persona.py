#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para gerar Campo Semântico da Persona a partir dos comentários analisados
Baseado nas instruções do arquivo instrucoes_para_gerar_persona.md
"""

import json
import os
from collections import Counter
from datetime import datetime
import random

# Importação opcional para Word
try:
    from docx import Document
    from docx.shared import Inches
    DOCX_DISPONIVEL = True
except ImportError:
    DOCX_DISPONIVEL = False
    print("⚠️ python-docx não instalado. Instale com: pip install python-docx")

def carregar_comentarios(caminho_arquivo):
    """
    Etapa 1: Carregamento do arquivo JSON
    """
    print(f"📂 Carregando arquivo: {caminho_arquivo}")
    
    if not os.path.exists(caminho_arquivo):
        raise FileNotFoundError(f"Arquivo não encontrado: {caminho_arquivo}")
    
    # Verificar tamanho do arquivo
    tamanho_mb = os.path.getsize(caminho_arquivo) / (1024 * 1024)
    print(f"📊 Tamanho do arquivo: {tamanho_mb:.2f} MB")
    
    if tamanho_mb > 500:
        print("⚠️ Arquivo muito grande! Considere usar ijson para leitura incremental.")
    
    with open(caminho_arquivo, 'r', encoding='utf-8') as f:
        comentarios = json.load(f)
    
    if not isinstance(comentarios, list):
        raise ValueError("O arquivo deve conter uma lista de comentários")
    
    print(f"✅ Total de comentários carregados: {len(comentarios)}")
    return comentarios

def filtrar_comentarios_uteis(comentarios):
    """
    Etapa 2: Filtrar apenas os comentários úteis
    """
    print("\n🔍 Filtrando comentários úteis...")
    
    comentarios_uteis = [
        comentario for comentario in comentarios 
        if comentario.get("comentario_util") == True
    ]
    
    print(f"✅ Comentários úteis encontrados: {len(comentarios_uteis)}")
    print(f"📊 Percentual de comentários úteis: {len(comentarios_uteis)/len(comentarios)*100:.1f}%")
    
    return comentarios_uteis

def agrupar_e_contar_campos(comentarios_uteis):
    """
    Etapa 3: Agrupar e contar campos-chave
    """
    print("\n📊 Agrupando e contando campos-chave...")
    
    # Inicializar contadores
    contadores = {
        'categorias': Counter(),
        'medos': Counter(),
        'dificuldades': Counter(),
        'valores': Counter(),
        'sonhos': Counter(),
        'emocoes': Counter(),
        'linguagem': Counter(),
        'ideias_conteudo': Counter(),
        'oportunidades_negocio': Counter()
    }
    
    # Listas para dados qualitativos
    motivacoes = []
    resumos = []
    
    for comentario in comentarios_uteis:
        # Campos de string única
        if comentario.get('categoria'):
            contadores['categorias'][comentario['categoria'].lower().strip()] += 1
        
        if comentario.get('emocao_principal'):
            contadores['emocoes'][comentario['emocao_principal'].lower().strip()] += 1
        
        # Campos de lista
        for campo_lista, contador in [
            ('medos', 'medos'),
            ('dificuldades', 'dificuldades'), 
            ('valores', 'valores'),
            ('sonhos', 'sonhos'),
            ('linguagem', 'linguagem'),
            ('ideias_conteudo', 'ideias_conteudo'),
            ('oportunidades_negocio', 'oportunidades_negocio')
        ]:
            if comentario.get(campo_lista):
                for item in comentario[campo_lista]:
                    if item and item.strip():
                        contadores[contador][item.lower().strip()] += 1
        
        # Dados qualitativos
        if comentario.get('motivacao'):
            motivacoes.append(comentario['motivacao'])
        
        if comentario.get('resumo'):
            resumos.append(comentario['resumo'])
    
    # Imprimir estatísticas
    for nome, contador in contadores.items():
        print(f"📈 {nome.capitalize()}: {len(contador)} itens únicos")
    
    print(f"📝 Motivações coletadas: {len(motivacoes)}")
    print(f"📄 Resumos coletados: {len(resumos)}")
    
    return contadores, motivacoes, resumos

def gerar_bloco_top_n(counter_obj, titulo, n=100):
    """
    Função auxiliar para gerar blocos formatados dos top N itens
    """
    items_mais_comuns = counter_obj.most_common(n)
    
    if not items_mais_comuns:
        return f"\n## {titulo}\n\nNenhum item encontrado.\n"
    
    bloco = f"\n## {titulo}\n\n"
    
    for i, (item, count) in enumerate(items_mais_comuns, 1):
        bloco += f"{i:3d}. {item}: {count}\n"
    
    return bloco

def gerar_blocos_texto(contadores, motivacoes, resumos):
    """
    Etapa 4: Síntese dos dados (geração dos blocos)
    """
    print("\n📝 Gerando blocos de texto...")
    
    blocos = []
    
    # Cabeçalho
    data_atual = datetime.now().strftime("%d/%m/%Y às %H:%M")
    blocos.append(f"# 📘 Campo Semântico do Público – Projeto Autismo\n")
    blocos.append(f"**Gerado em:** {data_atual}\n")
    blocos.append(f"**Total de comentários analisados:** {sum(contadores['categorias'].values())}\n")
    
    # Gerar blocos para cada categoria
    blocos.append(gerar_bloco_top_n(contadores['categorias'], "🏷️ Top 100 Categorias Mais Citadas"))
    blocos.append(gerar_bloco_top_n(contadores['medos'], "😰 Top 100 Medos Mais Frequentes"))
    blocos.append(gerar_bloco_top_n(contadores['dificuldades'], "⚠️ Top 100 Dificuldades Mais Relatadas"))
    blocos.append(gerar_bloco_top_n(contadores['valores'], "💎 Top 100 Valores Mais Importantes"))
    blocos.append(gerar_bloco_top_n(contadores['sonhos'], "✨ Top 100 Sonhos e Aspirações"))
    blocos.append(gerar_bloco_top_n(contadores['emocoes'], "😊 Frequência das Emoções Principais"))
    blocos.append(gerar_bloco_top_n(contadores['linguagem'], "💬 Top 100 Expressões Reais Mais Frequentes"))
    blocos.append(gerar_bloco_top_n(contadores['ideias_conteudo'], "💡 Top 100 Ideias de Conteúdo"))
    blocos.append(gerar_bloco_top_n(contadores['oportunidades_negocio'], "💰 Top 100 Oportunidades de Produtos Digitais"))
    
    # Amostra de resumos
    if resumos:
        resumos_amostra = random.sample(resumos, min(100, len(resumos)))
        bloco_resumos = "\n## 📄 100 Resumos Mais Representativos\n\n"
        for i, resumo in enumerate(resumos_amostra, 1):
            bloco_resumos += f"{i:3d}. {resumo}\n"
        blocos.append(bloco_resumos)
    
    return "\n".join(blocos)

def criar_pasta_saida(pasta="persona"):
    """
    Cria a pasta de saída se não existir
    """
    if not os.path.exists(pasta):
        os.makedirs(pasta)
        print(f"📁 Pasta criada: {pasta}")
    return pasta

def exportar_documento(conteudo, pasta_saida, nome_arquivo="campo_semantico_persona"):
    """
    Etapa 5: Exportar o Campo Semântico como Documento
    """
    print(f"\n💾 Exportando documento...")

    arquivos_criados = []

    # Exportar como Markdown
    arquivo_md = os.path.join(pasta_saida, f"{nome_arquivo}.md")
    with open(arquivo_md, 'w', encoding='utf-8') as f:
        f.write(conteudo)
    print(f"✅ Arquivo Markdown salvo: {arquivo_md}")
    arquivos_criados.append(arquivo_md)

    # Exportar como TXT
    arquivo_txt = os.path.join(pasta_saida, f"{nome_arquivo}.txt")
    with open(arquivo_txt, 'w', encoding='utf-8') as f:
        f.write(conteudo)
    print(f"✅ Arquivo TXT salvo: {arquivo_txt}")
    arquivos_criados.append(arquivo_txt)

    # Exportar como Word (se disponível)
    if DOCX_DISPONIVEL:
        arquivo_docx = exportar_word(conteudo, pasta_saida, nome_arquivo)
        arquivos_criados.append(arquivo_docx)

    return arquivos_criados

def exportar_word(conteudo, pasta_saida, nome_arquivo):
    """
    Exporta o conteúdo como documento Word (.docx)
    """
    arquivo_docx = os.path.join(pasta_saida, f"{nome_arquivo}.docx")
    doc = Document()

    # Dividir conteúdo em linhas
    linhas = conteudo.split('\n')

    for linha in linhas:
        linha = linha.strip()
        if not linha:
            continue

        # Título principal
        if linha.startswith('# '):
            titulo = linha[2:]
            doc.add_heading(titulo, level=1)

        # Subtítulos
        elif linha.startswith('## '):
            subtitulo = linha[3:]
            doc.add_heading(subtitulo, level=2)

        # Texto em negrito
        elif linha.startswith('**') and linha.endswith('**'):
            p = doc.add_paragraph()
            run = p.add_run(linha[2:-2])
            run.bold = True

        # Itens de lista numerada
        elif linha and linha[0].isdigit() and '. ' in linha:
            doc.add_paragraph(linha, style='List Number')

        # Texto normal
        else:
            doc.add_paragraph(linha)

    doc.save(arquivo_docx)
    print(f"✅ Arquivo Word salvo: {arquivo_docx}")
    return arquivo_docx

def exportar_dados_estruturados(contadores, pasta_saida, nome_base="dados_persona"):
    """
    Etapa 6: Exportação de dados estruturados
    """
    print(f"\n📊 Exportando dados estruturados...")

    arquivos_criados = []

    for nome, contador in contadores.items():
        if contador:
            # Exportar como TXT
            nome_arquivo_txt = os.path.join(pasta_saida, f"{nome_base}_{nome}.txt")
            with open(nome_arquivo_txt, 'w', encoding='utf-8') as f:
                f.write(f"# {nome.upper()}\n\n")
                for item, count in contador.most_common():
                    f.write(f"{item}: {count}\n")

            arquivos_criados.append(nome_arquivo_txt)
            print(f"✅ Arquivo TXT criado: {nome_arquivo_txt}")

            # Exportar como Word (se disponível)
            if DOCX_DISPONIVEL:
                nome_arquivo_docx = os.path.join(pasta_saida, f"{nome_base}_{nome}.docx")
                doc = Document()
                doc.add_heading(nome.upper(), level=1)

                for item, count in contador.most_common():
                    p = doc.add_paragraph()
                    p.add_run(f"{item}: ").bold = True
                    p.add_run(str(count))

                doc.save(nome_arquivo_docx)
                arquivos_criados.append(nome_arquivo_docx)
                print(f"✅ Arquivo Word criado: {nome_arquivo_docx}")

    return arquivos_criados

def main():
    """
    Função principal que executa todo o pipeline
    """
    print("🚀 Iniciando geração do Campo Semântico da Persona\n")
    
    # Configurações
    caminho_comentarios = "Mayra Gaiato _ Desenvolvimento Infantil e Autismo/analises/comentarios.json"
    
    try:
        # Criar pasta de saída dentro do projeto
        pasta_projeto = "Mayra Gaiato _ Desenvolvimento Infantil e Autismo"
        pasta_saida = criar_pasta_saida(os.path.join(pasta_projeto, "persona"))

        # Etapa 1: Carregar comentários
        comentarios = carregar_comentarios(caminho_comentarios)

        # Etapa 2: Filtrar comentários úteis
        comentarios_uteis = filtrar_comentarios_uteis(comentarios)

        if not comentarios_uteis:
            print("❌ Nenhum comentário útil encontrado!")
            return

        # Etapa 3: Agrupar e contar campos
        contadores, motivacoes, resumos = agrupar_e_contar_campos(comentarios_uteis)

        # Etapa 4: Gerar blocos de texto
        conteudo_documento = gerar_blocos_texto(contadores, motivacoes, resumos)

        # Etapa 5: Exportar documento principal
        arquivos_principais = exportar_documento(conteudo_documento, pasta_saida)

        # Etapa 6: Exportar dados estruturados
        arquivos_dados = exportar_dados_estruturados(contadores, pasta_saida)

        print(f"\n🎉 Processo concluído com sucesso!")
        print(f"📁 Arquivos gerados:")
        todos_arquivos = arquivos_principais + arquivos_dados
        for arquivo in todos_arquivos:
            print(f"   - {arquivo}")

        print(f"\n📊 Resumo:")
        print(f"   - Total de arquivos: {len(todos_arquivos)}")
        print(f"   - Comentários processados: {len(comentarios_uteis)}")
        print(f"   - Categorias únicas: {len(contadores['categorias'])}")
        print(f"   - Medos únicos: {len(contadores['medos'])}")
        print(f"   - Valores únicos: {len(contadores['valores'])}")
        
    except Exception as e:
        print(f"❌ Erro durante a execução: {e}")
        raise

if __name__ == "__main__":
    main()
